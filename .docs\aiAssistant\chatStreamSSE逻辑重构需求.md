# chatStreamSSE 逻辑重构需求

## 背景

当前的 `chatStreamSSE` 函数只能进行简单的意图识别和内容返回，缺乏实际的任务执行能力。为了实现真正的智能任务助手功能，需要重构该函数，使其能够根据用户意图调用相应的工具函数来执行具体的任务操作。

这个重构将使 AI 助理从单纯的对话工具升级为能够实际执行任务管理操作的智能助手，大大提升用户体验和功能实用性。

## 需求

### 功能需求

#### 1. 意图分类与处理

- **简化意图分类**：将原有的 `create_task`、`find_task` 合并为统一的 `task` 意图
- **两种意图类型**：系统只需识别 `task` 和 `chat` 两种意图类型
- **任务模式增强**：对于 `task` 意图，需要进行实际的工具函数调用
- **闲聊模式保持**：对于 `chat` 意图，保持现有的纯对话逻辑

#### 2. 工具函数集成

- **任务管理工具**：集成滴答清单 API 的任务相关函数
- **项目管理工具**：集成滴答清单 API 的项目相关函数
- **执行计划生成**：根据用户意图自动生成具体的执行步骤

#### 3. 流式响应优化

- **保持 SSE 机制**：确保流式推送功能正常工作
- **增加执行状态推送**：推送工具函数执行状态和结果
- **错误处理增强**：完善工具函数调用的错误处理机制

### 非功能需求

- **性能要求**：工具函数调用不应显著影响响应速度
- **可靠性要求**：确保工具函数调用失败时有合适的降级处理
- **可扩展性要求**：架构设计应便于后续添加更多工具函数

## 技术方案

### 实现思路

1. **意图识别阶段**：AI 识别用户输入为 `task` 或 `chat` 两种意图之一
2. **计划生成阶段**：对于 `task` 意图，根据用户输入生成具体的执行计划
3. **工具调用阶段**：按照计划依次调用相应的工具函数
4. **结果整合阶段**：将工具函数执行结果整合并通过 SSE 推送给前端

### 架构设计

```mermaid
graph TD
    A[用户输入] --> B[意图识别]
    B --> C{意图类型}
    C -->|chat| D[直接AI对话]
    C -->|task| E[分析任务需求]
    E --> F[生成执行计划]
    F --> G[调用工具函数]
    G --> H[整合执行结果]
    H --> I[SSE流式推送]
    D --> I

    J[工具函数库] --> G
    K[任务管理工具] --> J
    L[项目管理工具] --> J

    subgraph "任务处理流程"
        E --> M[创建任务]
        E --> N[查询任务]
        E --> O[更新任务]
        E --> P[删除任务]
        E --> Q[项目管理]
    end
```

### 可用工具函数

来自 `uniCloud-aliyun/cloudfunctions/dida-todo/index.obj.js` 的工具函数：

**任务管理相关：**

- `getTask(params)` - 获取任务详情
- `getTasks(params)` - 获取任务列表
- `createTask(params)` - 创建任务
- `updateTask(params)` - 更新任务
- `deleteTask(params)` - 删除任务
- `completeTask(params)` - 完成任务

**项目管理相关：**

- `getProject(params)` - 获取项目详情
- `getProjectData(params)` - 获取项目及其数据
- `getProjects()` - 获取项目列表
- `createProject(params)` - 创建项目
- `updateProject(params)` - 更新项目
- `deleteProject(params)` - 删除项目

### 技术栈与约束

- **云函数框架**：uniCloud
- **AI 模型**：豆包 (doubao-seed-1-6-250615)
- **流式推送**：SSE (Server-Sent Events)
- **API 集成**：滴答清单开放 API
- **性能约束**：单次工具函数调用不超过 10 秒
- **并发约束**：避免同时调用多个可能冲突的工具函数

### 实现细节

#### 1. 工具函数调用逻辑

```javascript
// 伪代码示例
async function executeTaskPlan(intentContent, sseChannel) {
  // 统一的 task 意图处理，不再区分 create_task 和 find_task
  const plan = generateExecutionPlan('task', intentContent)

  for (const step of plan) {
    await sseChannel.write({
      type: 'execution_step',
      step: step.description,
      timestamp: Date.now(),
    })

    const result = await callToolFunction(step.tool, step.params)

    await sseChannel.write({
      type: 'step_result',
      result: result,
      timestamp: Date.now(),
    })
  }
}
```

#### 2. SSE 消息类型扩展

- `execution_plan` - 推送执行计划
- `execution_step` - 推送当前执行步骤
- `step_result` - 推送步骤执行结果
- `execution_complete` - 推送整体执行完成

## 风险评估

### 假设与未知因素

- **假设**：滴答清单 API 稳定可用，响应时间在可接受范围内
- **假设**：用户输入的任务描述足够清晰，能够准确识别意图
- **未知因素**：复杂任务场景下的工具函数组合调用效果
- **未知因素**：高并发情况下的系统稳定性

### 潜在风险

1. **API 调用失败风险**

   - **风险描述**：滴答清单 API 不可用或响应超时
   - **应对策略**：实现重试机制和降级处理，API 失败时回退到纯对话模式

2. **意图识别错误风险**

   - **风险描述**：AI 错误识别用户意图，导致执行错误操作
   - **应对策略**：增加确认机制，对于重要操作（如删除）需要用户二次确认

3. **性能影响风险**

   - **风险描述**：工具函数调用导致响应时间过长
   - **应对策略**：设置合理的超时时间，优化工具函数调用逻辑

4. **数据一致性风险**
   - **风险描述**：多个工具函数调用之间可能存在数据不一致
   - **应对策略**：设计合理的事务处理机制，确保操作的原子性

### 解决方案

- **监控告警**：添加工具函数调用的监控和告警机制
- **日志记录**：详细记录所有工具函数调用的参数和结果
- **测试覆盖**：编写全面的单元测试和集成测试
- **渐进式发布**：先在小范围用户中测试，逐步扩大使用范围
