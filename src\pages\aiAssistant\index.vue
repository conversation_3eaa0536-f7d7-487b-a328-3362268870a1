<template>
  <view class="ai-chat-page">
    <!-- 导航栏 -->
    <z-page-navbar title="AI 助手" />
    <l-message-list
      ref="messageListRef"
      :messages="messages"
      @task-confirm="handleTaskConfirm"
      @task-cancel="handleTaskCancel"
    />
    <view class="message-input-wrapper">
      <z-message-input
        v-model="inputValue"
        @send="handleSendMessageStream"
        @send-audio="handleSendAudio"
        placeholder="有什么想问的..."
        cloud-path="aiAssistant/"
        :disabled="isStreaming"
      />
    </view>
  </view>
</template>

<script setup>
import { ref } from 'vue'
import LMessageList from './components/l-message-list.vue'
import ZMessageInput from '@/components/z-message-input/z-message-input.vue'
import { nextTick } from 'vue'

const inputValue = ref('')
const messageListRef = ref(null)
const messages = ref([
  {
    _id: '1',
    content: '你好！我是你的 AI 助手，有什么可以帮助你的吗？',
    type: 'text',
    isUser: false,
    time: new Date().toISOString(),
  },
])

const aiApi = uniCloud.importObject('ai', {
  customUI: true, // 取消自动展示的交互提示界面
})

// 流式聊天状态管理
const isStreaming = ref(false)
const currentStreamingMessageId = ref(null)
const connectionStatus = ref('disconnected')
const retryCount = ref(0)
const maxRetries = 3

const addMessage = (message) => {
  messages.value.push({
    ...message,
    _id: Date.now().toString(),
    time: new Date().toISOString(),
  })
  nextTick(() => {
    messageListRef.value?.scrollToBottom()
  })
}

// 处理流式消息接收
const handleStreamMessage = (data) => {
  console.log('收到流式消息：', data)

  const loadingIndex = messages.value.findIndex((m) => m.loading)

  switch (data.type) {
    case 'start':
      if (loadingIndex !== -1) {
        messages.value.splice(loadingIndex, 1)
      }
      const startMessage = {
        _id: Date.now().toString(),
        content: '意图识别中...',
        type: 'text',
        isUser: false,
        streaming: true,
        statusMessage: true,
        time: new Date().toISOString(),
      }
      messages.value.push(startMessage)
      currentStreamingMessageId.value = startMessage._id
      isStreaming.value = true

      nextTick(() => {
        messageListRef.value?.scrollToBottom()
      })
      break

    case 'intent_type':
      // 处理意图类型，更新状态为对应的处理中状态
      if (currentStreamingMessageId.value) {
        const messageIndex = messages.value.findIndex((msg) => msg._id === currentStreamingMessageId.value)
        if (messageIndex !== -1) {
          messages.value[messageIndex].intentType = data.intentType
          console.log('intentType', data.intentType)
          // 根据不同意图类型更新状态提示
          switch (data.intentType) {
            case 'create_task':
              messages.value[messageIndex].content = '创建任务中...'
              // 标记这是任务创建意图，后续需要确认
              messages.value[messageIndex].needConfirm = true
              break
            case 'find_task':
              messages.value[messageIndex].content = '查询任务中...'
              break
            case 'chat':
              messages.value[messageIndex].content = '思考回复中...'
              break
            default:
              messages.value[messageIndex].content = '处理中...'
          }

          nextTick(() => {
            messageListRef.value?.scrollToBottom()
          })
        }
      }
      break

    case 'intent_content_start':
      // 接收到内容开始，隐藏状态提示，显示实际内容
      if (currentStreamingMessageId.value) {
        const messageIndex = messages.value.findIndex((msg) => msg._id === currentStreamingMessageId.value)
        if (messageIndex !== -1) {
          // 移除状态标记
          messages.value[messageIndex].statusMessage = false

          // 如果是任务创建意图，不直接显示内容而是存储识别内容用于确认
          if (messages.value[messageIndex].intentType === 'create_task' && messages.value[messageIndex].needConfirm) {
            messages.value[messageIndex].recognizedContent = data.content
            messages.value[messageIndex].content = '我识别到您想创建一个任务，请确认以下信息是否正确：'
            messages.value[messageIndex].showConfirmCard = true
          } else {
            // 其他意图正常显示内容
            messages.value[messageIndex].content = data.content
          }

          nextTick(() => {
            messageListRef.value?.scrollToBottom()
          })
        }
      }
      break

    case 'intent_content_chunk':
      // 处理内容块，累加显示
      if (currentStreamingMessageId.value) {
        const messageIndex = messages.value.findIndex((msg) => msg._id === currentStreamingMessageId.value)
        if (messageIndex !== -1) {
          // 如果是任务创建意图且需要确认，则累积到 recognizedContent 而不是直接显示
          if (messages.value[messageIndex].intentType === 'create_task' && messages.value[messageIndex].needConfirm) {
            messages.value[messageIndex].recognizedContent =
              (messages.value[messageIndex].recognizedContent || '') + data.content
          } else {
            messages.value[messageIndex].content += data.content
          }

          nextTick(() => {
            messageListRef.value?.scrollToBottom()
          })
        }
      }
      break

    case 'chunk':
      // 处理旧的 chunk 类型消息（保留向后兼容性）
      if (currentStreamingMessageId.value) {
        const messageIndex = messages.value.findIndex((msg) => msg._id === currentStreamingMessageId.value)
        if (messageIndex !== -1) {
          messages.value[messageIndex].content = data.fullContent || data.content

          nextTick(() => {
            messageListRef.value?.scrollToBottom()
          })
        }
      }
      break

    case 'end':
      if (loadingIndex !== -1) {
        messages.value.splice(loadingIndex, 1)
      }
      if (currentStreamingMessageId.value) {
        const messageIndex = messages.value.findIndex((msg) => msg._id === currentStreamingMessageId.value)
        if (messageIndex !== -1) {
          // 对于任务创建意图，保持确认卡片状态
          if (messages.value[messageIndex].intentType === 'create_task' && messages.value[messageIndex].needConfirm) {
            // 保留当前状态，不覆盖 content
            messages.value[messageIndex].streaming = false
            messages.value[messageIndex].statusMessage = false
          } else {
            // 其他意图正常处理
            messages.value[messageIndex].content = data.content
            messages.value[messageIndex].streaming = false
            messages.value[messageIndex].statusMessage = false
          }
        }
      }
      isStreaming.value = false
      currentStreamingMessageId.value = null
      console.log('流式数据接收完成')
      break

    case 'error':
      if (loadingIndex !== -1) {
        messages.value.splice(loadingIndex, 1)
      }

      if (currentStreamingMessageId.value) {
        const messageIndex = messages.value.findIndex((msg) => msg._id === currentStreamingMessageId.value)
        if (messageIndex !== -1) {
          messages.value[messageIndex].content = `抱歉，发生了错误：${data.error}`
          messages.value[messageIndex].streaming = false
          messages.value[messageIndex].statusMessage = false
        }
      } else {
        addMessage({
          content: `抱歉，发生了错误：${data.error}`,
          type: 'text',
          isUser: false,
        })
      }
      isStreaming.value = false
      currentStreamingMessageId.value = null
      console.error('流式聊天错误：', data.error)
      break
  }
}

// 带重试机制和超时处理的流式聊天
const handleSendMessageStreamWithRetry = async (userMessage, historyMessages, attempt = 1) => {
  let timeoutId = null

  try {
    connectionStatus.value = 'connecting'
    console.time('uniCloud.start')
    console.time('uniCloud.SSEChannel')
    const channel = new uniCloud.SSEChannel()
    console.timeEnd('uniCloud.SSEChannel')
    // 设置 5 分钟超时
    timeoutId = setTimeout(() => {
      console.log('流式聊天超时，5 分钟未收到响应')
      handleStreamMessage({
        type: 'error',
        error: '请求超时（5 分钟），请重新尝试',
      })
      channel.close()
    }, 300000)

    channel.on('open', () => {
      console.log('SSE Channel 连接成功')
      connectionStatus.value = 'connected'
      retryCount.value = 0
    })

    channel.on('message', (data) => {
      if (timeoutId) {
        clearTimeout(timeoutId)
        timeoutId = null
      }
      handleStreamMessage(data)
    })

    channel.on('end', (data) => {
      if (timeoutId) {
        clearTimeout(timeoutId)
        timeoutId = null
      }
      handleStreamMessage(data)
    })

    channel.on('error', (error) => {
      console.error('SSE Channel 错误：', error)
      connectionStatus.value = 'error'

      if (timeoutId) {
        clearTimeout(timeoutId)
        timeoutId = null
      }

      if (attempt < maxRetries) {
        console.log(`连接失败，准备第${attempt + 1}次重试...`)
        setTimeout(() => {
          handleSendMessageStreamWithRetry(userMessage, historyMessages, attempt + 1)
        }, 1000 * attempt)
      } else {
        handleStreamMessage({
          type: 'error',
          error: `连接失败，已重试${maxRetries}次：${error.message || '连接错误'}`,
        })
      }
    })

    channel.on('close', () => {
      console.log('SSE Channel 连接关闭')
      if (connectionStatus.value !== 'error') {
        connectionStatus.value = 'disconnected'
      }
      if (timeoutId) {
        clearTimeout(timeoutId)
        timeoutId = null
      }
    })
    console.time('uniCloud.open')
    await channel.open()
    console.timeEnd('uniCloud.open')
    console.log('SSE Channel 已开启')
    console.timeEnd('uniCloud.start')
    console.time('uniCloud.chatStreamSSE')
    const response = await aiApi.chatStreamSSE({
      message: userMessage,
      messages: historyMessages,
      channel: channel,
    })
    console.timeEnd('uniCloud.chatStreamSSE')
    console.log('流式聊天接口调用结果：', response)

    if (response.errCode !== 0) {
      throw new Error(response.errMsg || '调用 AI 接口失败')
    }
  } catch (error) {
    console.error(`流式聊天失败（第${attempt}次尝试）：`, error)
    connectionStatus.value = 'error'

    if (timeoutId) {
      clearTimeout(timeoutId)
      timeoutId = null
    }

    if (attempt < maxRetries) {
      console.log(`接口调用失败，准备第${attempt + 1}次重试...`)
      retryCount.value = attempt
      setTimeout(() => {
        handleSendMessageStreamWithRetry(userMessage, historyMessages, attempt + 1)
      }, 1000 * attempt)
    } else {
      handleStreamMessage({
        type: 'error',
        error: `请求失败，已重试${maxRetries}次：${error.message || '网络连接异常，请检查网络后重试'}`,
      })
    }
  }
}

// 使用 SSE Channel 的流式聊天
const handleSendMessageStream = async () => {
  if (!inputValue.value.trim() || isStreaming.value) return

  addMessage({
    content: inputValue.value,
    type: 'text',
    isUser: true,
  })

  const userMessage = inputValue.value
  inputValue.value = ''

  addMessage({
    content: '思考中...',
    type: 'text',
    isUser: false,
    loading: true,
  })

  const historyMessages = messages.value
    .filter((msg) => !msg.loading && !msg.streaming && msg.type === 'text')
    .map((msg) => ({
      role: msg.isUser ? 'user' : 'assistant',
      content: msg.content,
    }))

  await handleSendMessageStreamWithRetry(userMessage, historyMessages)
}

const handleSendAudio = (audioData) => {
  addMessage({
    type: 'audio',
    isUser: true,
    audioUrl: audioData.tempFileURL,
    audioDuration: audioData.duration,
  })

  addMessage({
    type: 'text',
    isUser: false,
    loading: true,
  })

  setTimeout(() => {
    messages.value = messages.value.filter((m) => !m.loading)
    addMessage({
      content: '我收到了你的语音，正在思考如何回复...',
      type: 'text',
      isUser: false,
    })
  }, 1500)
}

// 处理任务确认
const handleTaskConfirm = async ({ messageId, content }) => {
  console.log('确认创建任务：', content)

  // 查找并更新消息
  const messageIndex = messages.value.findIndex((msg) => msg._id === messageId)
  if (messageIndex !== -1) {
    // 更新消息状态，不再显示确认卡片
    messages.value[messageIndex].showConfirmCard = false
    messages.value[messageIndex].content = '正在创建任务...'

    try {
      // 调用 dida-todo 云函数的创建任务方法
      const didaTodoApi = uniCloud.importObject('dida-todo', {
        customUI: true,
      })

      const result = await didaTodoApi.createTask({
        taskData: {
          title: content,
        },
      })

      if (result.success) {
        // 创建成功，更新消息
        messages.value[messageIndex].content = `✅ 任务已创建成功!\n\n${content}`
      } else {
        // 创建失败，显示错误信息
        messages.value[messageIndex].content = `创建任务失败：${result.errMsg || '未知错误'}`
      }
    } catch (error) {
      console.error('创建任务失败：', error)
      // 创建失败，显示错误信息
      messages.value[messageIndex].content = `创建任务失败：${error.message || '未知错误'}`
    }
  }
}

// 处理任务取消
const handleTaskCancel = ({ messageId }) => {
  console.log('取消创建任务：', messageId)

  // 查找并更新消息
  const messageIndex = messages.value.findIndex((msg) => msg._id === messageId)
  if (messageIndex !== -1) {
    // 更新消息状态，不再显示确认卡片
    messages.value[messageIndex].showConfirmCard = false
    messages.value[messageIndex].content = `已取消创建任务。如果我理解有误，请尝试重新描述您的需求。`
  }
}
</script>

<style lang="scss" scoped>
.ai-chat-page {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f4f5f7;
}

.message-input-wrapper {
  background-color: #fff;
}
</style>
