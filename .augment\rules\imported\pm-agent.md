---
type: 'manual'
---

# 必须严格遵循要求和核心记忆程序规则！绝对不允许修改 `.docs` 文件夹之外的任何文件 ​​！

# 需求文档管理规范

## 使用场景

- 当用户请求创建新的需求文档时
- 当用户需要修改现有需求文档时
- 当需要查询需求文档结构规范时

## 关键规则

- 需求文档必须创建在 `.docs` 目录下
- 需求文档必须按功能模块分文件夹存放，如 `.docs/编辑器/`、`.docs/用户管理/` 等
- 需求文档命名格式为：`需求名称.md`
- 已完成或不再活跃的需求文档应移至 `.docs/归档` 目录
- 需求文档必须包含标准结构：背景、需求、技术方案
- 当用户提示词包含"创建需求文档"意图时，自动在 `.docs` 对应功能模块目录下创建文档，如果目录不存在则先创建目录
- 当用户提示词包含"修改需求文档"意图时，自动修改已有文档
- 文件修改权限限制：​​ ​​ 你绝对不允许修改 `.docs` 文件夹之外的任何文件 ​​
- 组件命名规范：全局组件（放在 `src/components/` 下）以 `z-` 开头，局部组件（放在页面的 components 目录下）以 `l-` 开头
- 组件位置规范：页面特定组件应放在对应页面的 components 目录下（如 `src/pages/okr/components/`），而不是放在全局组件目录

## 需求文档结构规范

需求文档应包含以下基本结构：

1. 背景：描述需求来源、目的和价值
2. 需求：功能需求（必须）和非功能需求（可选）
3. 技术方案：
   - 前端实现思路详情
   - 如有必要，使用 mermaid 图表可视化架构和流程
   - 详细的技术栈、设置和约束条件
4. 风险评估：
   - 未知因素和假设
   - 潜在风险和应对策略

## 新增需求

### chatStreamSSE 逻辑重构需求

**需求描述：** 重构 `uniCloud-aliyun/cloudfunctions/ai/index.obj.js` 中的 `chatStreamSSE` 函数逻辑，根据用户输入判断意图后，根据工具列出计划并执行。

**意图类型分类：**

- `task` 模式：任务相关操作，需要根据提示词里提供的工具，列出执行计划
- `chat` 模式：闲聊模式，保持现有逻辑不变

**可用工具函数：** 来自 `uniCloud-aliyun/cloudfunctions/dida-todo/index.obj.js` 的以下函数：

- `getTask(params)` - 获取任务详情
- `getTasks(params)` - 获取任务列表
- `createTask(params)` - 创建任务
- `updateTask(params)` - 更新任务
- `deleteTask(params)` - 删除任务
- `completeTask(params)` - 完成任务
- `getProject(params)` - 获取项目详情
- `getProjectData(params)` - 获取项目及其数据
- `getProjects()` - 获取项目列表
- `createProject(params)` - 创建项目
- `updateProject(params)` - 更新项目
- `deleteProject(params)` - 删除项目

**实现要求：**

1. 保持现有的意图识别逻辑（create_task、find_task、chat）
2. 对于 task 模式（create_task 和 find_task），需要：
   - 分析用户意图，确定需要使用的工具函数
   - 生成执行计划（列出具体的执行步骤）
   - 按计划调用相应的工具函数
   - 返回执行结果
3. 对于 chat 模式，保持现有逻辑不变
4. 确保 SSE 流式推送机制正常工作
5. 保持错误处理和日志记录功能

## 示例

<example>
用户: "为在线编辑器功能创建需求文档"

操作:

1. 在 `.docs` 目录下检查是否存在 `编辑器` 文件夹，如不存在则创建
2. 在 `.docs/编辑器` 目录下创建 `在线编辑器功能.md` 文件
3. 使用标准结构模板创建文档内容:

````markdown
# 在线编辑器功能需求

## 背景

简要描述在线编辑器功能的目的和价值...

## 需求

### 功能需求

- 富文本编辑支持
- 实时保存
  ...

### 非功能需求

- 用户体验
  ...

## 技术方案

### 实现思路

详细描述前端实现思路...

### 架构设计

```mermaid
graph TD
    A[用户输入] --> B[编辑器核心]
    B --> C[本地存储]
    B --> D[云端同步]
    D --> E[数据库]
```
````

### 技术栈与约束

- 前端框架: Vue.js 3
- 编辑器库: TipTap
- 性能约束: 初始加载时间不超过 2 秒
  ...

## 风险评估

### 假设与未知因素

- 假设用户设备内存至少有 4GB
- 未知用户在弱网环境下的使用体验
  ...

### 潜在风险

- 数据丢失风险
- 兼容性问题
- 解决方案: 实现自动备份机制，全面的浏览器兼容性测试
  ...

````
</example>

<example type="invalid">
用户: "修改编辑器功能需求"

操作:
1. 直接在项目根目录创建 `编辑器功能.md`
2. 使用自定义结构:
```markdown
# 编辑器功能
以下是功能列表：
...
````

</example>
